import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

# Data directory
DATA_DIR = "data"

# Ensure data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

# File paths for storing data
CHANNELS_FILE = os.path.join(DATA_DIR, "channels.json")
PROJECTS_FILE = os.path.join(DATA_DIR, "projects.json")
CONTENT_TYPES_FILE = os.path.join(DATA_DIR, "content_types.json")
POST_HISTORY_FILE = os.path.join(DATA_DIR, "post_history.json")

# Image directory
IMAGE_DIR = os.path.join(DATA_DIR, "images")
os.makedirs(IMAGE_DIR, exist_ok=True)

def initialize_database():
    """Initialize the database files if they don't exist."""
    if not os.path.exists(CHANNELS_FILE):
        with open(CHANNELS_FILE, 'w') as f:
            json.dump({}, f)

    if not os.path.exists(PROJECTS_FILE):
        with open(PROJECTS_FILE, 'w') as f:
            json.dump({}, f)

    if not os.path.exists(CONTENT_TYPES_FILE):
        default_content_types = {
            "daily_news_summary": {
                "name": "Daily News Summary",
                "description": "Generates a daily summary of news for India",
                "parameters": {
                    "country": "India",
                    "post_interval_hours": 24,
                    "post_time_hour": 8,  # Default to 8 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            },
            "crypto_prices": {
                "name": "Crypto Prices",
                "description": "Posts daily cryptocurrency price updates",
                "parameters": {
                    "num_coins": 4,  # Number of main coins to display
                    "post_interval_hours": 24,
                    "post_time_hour": 9,  # Default to 9 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            },
            "cricket_news": {
                "name": "Cricket News and Updates",
                "description": "Generates engaging cricket news and updates",
                "parameters": {
                    "post_interval_hours": 24,
                    "post_time_hour": 10,  # Default to 10 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            },
            "custom_content": {
                "name": "Custom Content",
                "description": "Generates custom content using user-defined AI prompts",
                "parameters": {
                    "post_interval_hours": 24,
                    "post_time_hour": 11,  # Default to 11 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            }
        }
        with open(CONTENT_TYPES_FILE, 'w') as f:
            json.dump(default_content_types, f, indent=4)

    if not os.path.exists(POST_HISTORY_FILE):
        with open(POST_HISTORY_FILE, 'w') as f:
            json.dump({}, f)

def get_channels(user_id: int = None) -> Dict[str, Dict[str, Any]]:
    """Get channels from the database for a specific user."""
    if not os.path.exists(CHANNELS_FILE):
        return {}

    with open(CHANNELS_FILE, 'r') as f:
        try:
            all_channels = json.load(f)
        except json.JSONDecodeError:
            all_channels = {}

    # If no user_id provided, return empty dict (for security)
    if user_id is None:
        return {}

    # Return only channels for this user
    user_channels = {}
    for channel_id, channel_data in all_channels.items():
        if channel_data.get('user_id') == user_id:
            user_channels[channel_id] = channel_data

    return user_channels

def add_channel(user_id: int, channel_id: str, channel_name: str, channel_type: str) -> bool:
    """
    Add a channel to the database for a specific user.

    Args:
        user_id: The ID of the user adding the channel
        channel_id: The ID of the channel
        channel_name: The name of the channel
        channel_type: The type of the channel (public, private, etc.)

    Returns:
        bool: True if the channel was added successfully, False otherwise
    """
    # Get all channels (not filtered by user)
    if not os.path.exists(CHANNELS_FILE):
        all_channels = {}
    else:
        with open(CHANNELS_FILE, 'r') as f:
            try:
                all_channels = json.load(f)
            except json.JSONDecodeError:
                all_channels = {}

    # Check if this user already has this channel
    user_channels = get_channels(user_id)
    if channel_id in user_channels:
        return False

    # Add the channel with user_id
    all_channels[channel_id] = {
        "user_id": user_id,
        "name": channel_name,
        "type": channel_type,
        "added_at": datetime.now().isoformat()
    }

    # Save to file
    with open(CHANNELS_FILE, 'w') as f:
        json.dump(all_channels, f, indent=4)

    return True

def remove_channel(user_id: int, channel_id: str) -> bool:
    """Remove a channel from the database for a specific user."""
    # Get all channels
    if not os.path.exists(CHANNELS_FILE):
        return False

    with open(CHANNELS_FILE, 'r') as f:
        try:
            all_channels = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if channel exists and belongs to this user
    if channel_id not in all_channels:
        return False

    if all_channels[channel_id].get('user_id') != user_id:
        return False  # User doesn't own this channel

    # Remove the channel
    del all_channels[channel_id]

    with open(CHANNELS_FILE, 'w') as f:
        json.dump(all_channels, f, indent=4)

    return True

def get_projects(user_id: int = None) -> Dict[str, Dict[str, Any]]:
    """Get projects from the database for a specific user."""
    if not os.path.exists(PROJECTS_FILE):
        return {}

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            all_projects = {}

    # If no user_id provided, return empty dict (for security)
    if user_id is None:
        return {}

    # Return only projects for this user
    user_projects = {}
    for project_id, project_data in all_projects.items():
        if project_data.get('user_id') == user_id:
            user_projects[project_id] = project_data

    return user_projects

def get_all_projects_for_posting() -> Dict[str, Dict[str, Any]]:
    """Get all projects from the database for content posting (internal use only)."""
    if not os.path.exists(PROJECTS_FILE):
        return {}

    with open(PROJECTS_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def add_project(user_id: int, project_id: str, project_name: str, channels: List[Dict[str, Any]]) -> bool:
    """
    Add a project to the database for a specific user.

    Args:
        user_id: The ID of the user creating the project
        project_id: The ID of the project
        project_name: The name of the project
        channels: List of channel configurations for this project

    Returns:
        bool: True if the project was added successfully, False otherwise
    """
    # Get all projects (not filtered by user)
    if not os.path.exists(PROJECTS_FILE):
        all_projects = {}
    else:
        with open(PROJECTS_FILE, 'r') as f:
            try:
                all_projects = json.load(f)
            except json.JSONDecodeError:
                all_projects = {}

    # Check if this user already has this project
    user_projects = get_projects(user_id)
    if project_id in user_projects:
        return False

    # Add the project with user_id
    all_projects[project_id] = {
        "user_id": user_id,
        "name": project_name,
        "channels": channels,
        "created_at": datetime.now().isoformat(),
        "active": True
    }

    # Save to file
    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def update_project(user_id: int, project_id: str, data: Dict[str, Any]) -> bool:
    """Update a project in the database for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Update the project with new data
    for key, value in data.items():
        all_projects[project_id][key] = value

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def remove_project(user_id: int, project_id: str) -> bool:
    """Remove a project from the database for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Remove the project
    del all_projects[project_id]

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def get_content_types() -> Dict[str, Dict[str, Any]]:
    """Get all content types from the database."""
    if not os.path.exists(CONTENT_TYPES_FILE):
        return {}

    with open(CONTENT_TYPES_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def get_content_type(content_type_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific content type from the database."""
    content_types = get_content_types()
    return content_types.get(content_type_id)

def update_project_content_settings(user_id: int, project_id: str, content_type_id: str, settings: Dict[str, Any]) -> bool:
    """Update content settings for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Initialize content_settings if it doesn't exist
    if "content_settings" not in all_projects[project_id]:
        all_projects[project_id]["content_settings"] = {}

    # Update or add content type settings
    all_projects[project_id]["content_settings"][content_type_id] = settings

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def get_post_history(user_id: int = None) -> Dict[str, List[Dict[str, Any]]]:
    """Get post history from the database for a specific user."""
    if not os.path.exists(POST_HISTORY_FILE):
        return {}

    with open(POST_HISTORY_FILE, 'r') as f:
        try:
            all_history = json.load(f)
        except json.JSONDecodeError:
            all_history = {}

    # If no user_id provided, return empty dict (for security)
    if user_id is None:
        return {}

    # Return only history for projects owned by this user
    user_projects = get_projects(user_id)
    user_history = {}
    for project_id in user_projects.keys():
        if project_id in all_history:
            user_history[project_id] = all_history[project_id]

    return user_history

def add_post_to_history(user_id: int, project_id: str, channel_id: str, content_type: str, content: str) -> bool:
    """Add a post to the history for a specific user's project."""
    # Verify the user owns this project
    user_projects = get_projects(user_id)
    if project_id not in user_projects:
        return False  # User doesn't own this project

    # Get all history
    if not os.path.exists(POST_HISTORY_FILE):
        all_history = {}
    else:
        with open(POST_HISTORY_FILE, 'r') as f:
            try:
                all_history = json.load(f)
            except json.JSONDecodeError:
                all_history = {}

    # Initialize project history if it doesn't exist
    if project_id not in all_history:
        all_history[project_id] = []

    # Add post to history
    post_entry = {
        "channel_id": channel_id,
        "content_type": content_type,
        "content": content,
        "timestamp": datetime.now().isoformat(),
    }

    all_history[project_id].append(post_entry)

    # Limit history to last 100 posts per project
    if len(all_history[project_id]) > 100:
        all_history[project_id] = all_history[project_id][-100:]

    with open(POST_HISTORY_FILE, 'w') as f:
        json.dump(all_history, f, indent=4)

    return True

def clear_post_history(user_id: int, project_id: str) -> bool:
    """Clear the post history for a project for a specific user.

    Args:
        user_id: The ID of the user
        project_id: The ID of the project

    Returns:
        bool: True if the history was cleared successfully, False otherwise
    """
    # Verify the user owns this project
    user_projects = get_projects(user_id)
    if project_id not in user_projects:
        return False  # User doesn't own this project

    # Get all history
    if not os.path.exists(POST_HISTORY_FILE):
        return False

    with open(POST_HISTORY_FILE, 'r') as f:
        try:
            all_history = json.load(f)
        except json.JSONDecodeError:
            return False

    if project_id not in all_history:
        return False

    # Clear the history for this project
    all_history[project_id] = []

    with open(POST_HISTORY_FILE, 'w') as f:
        json.dump(all_history, f, indent=4)

    return True

def reset_last_posted_time(user_id: int, project_id: str, content_type: str) -> bool:
    """Reset the last posted time for a project's content type for a specific user.

    Args:
        user_id: The ID of the user
        project_id: The ID of the project
        content_type: The content type (e.g., 'daily_news_summary', 'crypto_prices')

    Returns:
        bool: True if the last posted time was reset successfully, False otherwise
    """
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Get content settings
    content_settings = all_projects[project_id].get('content_settings', {})

    # Check if the content type exists
    if content_type not in content_settings:
        return False

    # Reset the last posted time
    content_settings[content_type]['last_posted'] = None

    # Update the project
    all_projects[project_id]['content_settings'] = content_settings

    # Save to file
    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    # Also clear from memory
    try:
        import content_poster
        content_poster.clear_last_post_time(project_id)
    except ImportError:
        pass  # content_poster module not available

    return True

def update_project_image_settings(user_id: int, project_id: str, settings: Dict[str, Any]) -> bool:
    """Update image settings for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Update image settings
    all_projects[project_id]["image_settings"] = settings

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def update_project_button_settings(user_id: int, project_id: str, settings: Dict[str, Any]) -> bool:
    """Update button settings for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Update button settings
    all_projects[project_id]["button_settings"] = settings

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def get_project_custom_prompt(user_id: int, project_id: str) -> str:
    """Get custom prompt for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return None

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return None

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return None

    if all_projects[project_id].get('user_id') != user_id:
        return None  # User doesn't own this project

    # Get custom prompt
    return all_projects[project_id].get('custom_prompt')

def set_project_custom_prompt(user_id: int, project_id: str, prompt: str) -> bool:
    """Set custom prompt for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Set custom prompt
    all_projects[project_id]["custom_prompt"] = prompt

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

def remove_project_custom_prompt(user_id: int, project_id: str) -> bool:
    """Remove custom prompt for a project for a specific user."""
    # Get all projects
    if not os.path.exists(PROJECTS_FILE):
        return False

    with open(PROJECTS_FILE, 'r') as f:
        try:
            all_projects = json.load(f)
        except json.JSONDecodeError:
            return False

    # Check if project exists and belongs to this user
    if project_id not in all_projects:
        return False

    if all_projects[project_id].get('user_id') != user_id:
        return False  # User doesn't own this project

    # Remove custom prompt
    if "custom_prompt" in all_projects[project_id]:
        del all_projects[project_id]["custom_prompt"]

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(all_projects, f, indent=4)

    return True

# Initialize the database when the module is imported
initialize_database()
