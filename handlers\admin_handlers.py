"""
Admin command handlers for user management and bot administration.
"""
import logging
from telegram import Update
from telegram.ext import CallbackContext

import database as db
from admin_notifications import get_admin_notifier

# Enable logging
logger = logging.getLogger(__name__)

# Admin user ID
ADMIN_USER_ID = 1049516929

async def approve_user_command(update: Update, context: CallbackContext) -> None:
    """Handle /approve command to approve a user."""
    user = update.effective_user
    
    # Check if user is admin
    if user.id != ADMIN_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this command.")
        return
    
    # Check if user_id argument is provided
    if not context.args or len(context.args) != 1:
        await update.message.reply_text(
            "❌ Usage: /approve <user_id>\n"
            "Example: /approve 123456789"
        )
        return
    
    try:
        target_user_id = int(context.args[0])
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
        return
    
    # Check if user is already approved
    if db.is_user_approved(target_user_id):
        await update.message.reply_text(f"ℹ️ User {target_user_id} is already approved.")
        return
    
    # Approve the user
    success = db.approve_user(
        user_id=target_user_id,
        username="unknown",  # We don't have this info from command
        first_name="Unknown",  # We don't have this info from command
        approved_by_id=user.id
    )
    
    if success:
        await update.message.reply_text(
            f"✅ User {target_user_id} has been approved successfully!\n"
            f"They now have access to the bot."
        )
        
        # Notify admin
        notifier = get_admin_notifier()
        if notifier:
            await notifier.send_user_approved_notification(target_user_id)
    else:
        await update.message.reply_text(f"❌ Failed to approve user {target_user_id}.")

async def list_users_command(update: Update, context: CallbackContext) -> None:
    """Handle /users command to list all approved users."""
    user = update.effective_user
    
    # Check if user is admin
    if user.id != ADMIN_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this command.")
        return
    
    # Get approved users
    approved_users = db.get_approved_users()
    
    if not approved_users:
        await update.message.reply_text("📝 No approved users found.")
        return
    
    # Format user list
    user_list = "👥 *Approved Users:*\n\n"
    
    for user_id, user_data in approved_users.items():
        user_list += f"👤 *User ID:* {user_data['user_id']}\n"
        user_list += f"📝 *Username:* @{user_data.get('username', 'unknown')}\n"
        user_list += f"👤 *Name:* {user_data.get('first_name', 'Unknown')}\n"
        user_list += f"📅 *Approved:* {user_data.get('approved_at', 'Unknown')[:10]}\n"
        
        if user_data.get('is_admin'):
            user_list += f"🔑 *Role:* Admin\n"
        else:
            user_list += f"👤 *Role:* User\n"
        
        user_list += "\n"
    
    # Split message if too long
    if len(user_list) > 4000:
        # Send in chunks
        chunks = [user_list[i:i+4000] for i in range(0, len(user_list), 4000)]
        for i, chunk in enumerate(chunks):
            if i == 0:
                await update.message.reply_text(chunk, parse_mode="Markdown")
            else:
                await update.message.reply_text(f"*Continued...*\n\n{chunk}", parse_mode="Markdown")
    else:
        await update.message.reply_text(user_list, parse_mode="Markdown")

async def remove_user_command(update: Update, context: CallbackContext) -> None:
    """Handle /remove command to remove user access."""
    user = update.effective_user
    
    # Check if user is admin
    if user.id != ADMIN_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this command.")
        return
    
    # Check if user_id argument is provided
    if not context.args or len(context.args) != 1:
        await update.message.reply_text(
            "❌ Usage: /remove <user_id>\n"
            "Example: /remove 123456789"
        )
        return
    
    try:
        target_user_id = int(context.args[0])
    except ValueError:
        await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
        return
    
    # Check if trying to remove admin
    if target_user_id == ADMIN_USER_ID:
        await update.message.reply_text("❌ Cannot remove admin user access.")
        return
    
    # Check if user is approved
    if not db.is_user_approved(target_user_id):
        await update.message.reply_text(f"ℹ️ User {target_user_id} is not currently approved.")
        return
    
    # Get user info before removal
    approved_users = db.get_approved_users()
    user_info = approved_users.get(str(target_user_id), {})
    
    # Remove user approval
    success = db.remove_user_approval(
        user_id=target_user_id,
        removed_by_id=user.id
    )
    
    if success:
        await update.message.reply_text(
            f"✅ User {target_user_id} access has been removed successfully!\n"
            f"They no longer have access to the bot."
        )
        
        # Notify admin
        notifier = get_admin_notifier()
        if notifier:
            await notifier.send_user_removed_notification(
                target_user_id,
                user_info.get('username'),
                user_info.get('first_name')
            )
    else:
        await update.message.reply_text(f"❌ Failed to remove user {target_user_id} access.")

async def admin_help_command(update: Update, context: CallbackContext) -> None:
    """Handle /adminhelp command to show admin commands."""
    user = update.effective_user
    
    # Check if user is admin
    if user.id != ADMIN_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this command.")
        return
    
    help_text = """🔑 *Admin Commands:*

👤 *User Management:*
• `/approve <user_id>` - Approve a user to access the bot
• `/remove <user_id>` - Remove user access
• `/users` - List all approved users

📊 *Bot Information:*
• `/adminhelp` - Show this help message

💡 *Usage Examples:*
• `/approve 123456789` - Approve user with ID 123456789
• `/remove 123456789` - Remove access for user 123456789
• `/users` - Show all approved users

🔒 *Access Control:*
Only approved users can use the bot. Unauthorized users will see a message directing them to contact @the_titanium_admin for access.

⚠️ *Error Notifications:*
All bot errors are automatically sent to you with detailed information for debugging."""
    
    await update.message.reply_text(help_text, parse_mode="Markdown")
