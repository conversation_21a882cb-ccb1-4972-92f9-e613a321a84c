"""
User access control utilities and decorators.
"""
import logging
from functools import wraps
from typing import Callable, Any
from telegram import Update
from telegram.ext import CallbackContext

import database as db
from admin_notifications import notify_admin_unauthorized_access

# Enable logging
logger = logging.getLogger(__name__)

def require_approval(func: Callable) -> Callable:
    """Decorator to require user approval for command handlers."""
    @wraps(func)
    async def wrapper(update: Update, context: CallbackContext, *args, **kwargs) -> Any:
        user = update.effective_user
        
        # Check if user is approved
        if not db.is_user_approved(user.id):
            # Log unauthorized access
            db.log_unauthorized_access(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                command=update.message.text if update.message else "callback_query"
            )
            
            # Notify admin
            await notify_admin_unauthorized_access(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                command=update.message.text if update.message else "callback_query"
            )
            
            # Send access denied message
            access_denied_msg = (
                "🔒 *Access Denied*\n\n"
                "I am an auto-posting bot with many advanced features. "
                "This is a paid service. Please contact @the_titanium_admin to purchase access."
            )
            
            if update.message:
                await update.message.reply_text(access_denied_msg, parse_mode="Markdown")
            elif update.callback_query:
                await update.callback_query.answer("Access denied. Contact @the_titanium_admin for access.", show_alert=True)
            
            return None
        
        # User is approved, proceed with the original function
        return await func(update, context, *args, **kwargs)
    
    return wrapper

def require_approval_callback(func: Callable) -> Callable:
    """Decorator to require user approval for callback query handlers."""
    @wraps(func)
    async def wrapper(update: Update, context: CallbackContext, *args, **kwargs) -> Any:
        user = update.effective_user
        
        # Check if user is approved
        if not db.is_user_approved(user.id):
            # Log unauthorized access
            db.log_unauthorized_access(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                command=f"callback: {update.callback_query.data if update.callback_query else 'unknown'}"
            )
            
            # Notify admin
            await notify_admin_unauthorized_access(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                command=f"callback: {update.callback_query.data if update.callback_query else 'unknown'}"
            )
            
            # Send access denied message
            await update.callback_query.answer(
                "🔒 Access denied. This is a paid service. Contact @the_titanium_admin for access.",
                show_alert=True
            )
            
            return None
        
        # User is approved, proceed with the original function
        return await func(update, context, *args, **kwargs)
    
    return wrapper

async def check_user_access(update: Update, context: CallbackContext) -> bool:
    """Check if user has access and handle denial if not."""
    user = update.effective_user
    
    # Check if user is approved
    if not db.is_user_approved(user.id):
        # Log unauthorized access
        db.log_unauthorized_access(
            user_id=user.id,
            username=user.username,
            first_name=user.first_name,
            command=update.message.text if update.message else "unknown"
        )
        
        # Notify admin
        await notify_admin_unauthorized_access(
            user_id=user.id,
            username=user.username,
            first_name=user.first_name,
            command=update.message.text if update.message else "unknown"
        )
        
        # Send access denied message
        access_denied_msg = (
            "🔒 *Access Denied*\n\n"
            "I am an auto-posting bot with many advanced features. "
            "This is a paid service. Please contact @the_titanium_admin to purchase access."
        )
        
        if update.message:
            await update.message.reply_text(access_denied_msg, parse_mode="Markdown")
        elif update.callback_query:
            await update.callback_query.answer("Access denied. Contact @the_titanium_admin for access.", show_alert=True)
        
        return False
    
    return True

def is_admin(user_id: int) -> bool:
    """Check if user is admin."""
    return user_id == db.ADMIN_USER_ID

def require_admin(func: Callable) -> Callable:
    """Decorator to require admin privileges."""
    @wraps(func)
    async def wrapper(update: Update, context: CallbackContext, *args, **kwargs) -> Any:
        user = update.effective_user
        
        # Check if user is admin
        if not is_admin(user.id):
            if update.message:
                await update.message.reply_text("❌ You are not authorized to use this command.")
            elif update.callback_query:
                await update.callback_query.answer("❌ Admin access required.", show_alert=True)
            return None
        
        # User is admin, proceed with the original function
        return await func(update, context, *args, **kwargs)
    
    return wrapper
