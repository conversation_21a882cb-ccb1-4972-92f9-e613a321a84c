"""
Admin notification system for error handling and user access control.
"""
import logging
import traceback
from datetime import datetime
from typing import Optional, Any
from telegram import Bo<PERSON>
from telegram.error import TelegramError

import database as db

# Enable logging
logger = logging.getLogger(__name__)

# Admin user ID
ADMIN_USER_ID = 1049516929

class AdminNotifier:
    """Handles sending notifications to admin user."""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        self.admin_id = ADMIN_USER_ID
    
    async def send_error_notification(self, error: Exception, context: str = None, user_id: int = None, update_data: str = None) -> None:
        """Send error notification to admin."""
        try:
            # Format error message
            error_msg = f"🚨 *Bot Error Alert*\n\n"
            error_msg += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            if context:
                error_msg += f"📍 *Context:* {context}\n"
            
            if user_id:
                error_msg += f"👤 *User ID:* {user_id}\n"
            
            error_msg += f"❌ *Error Type:* {type(error).__name__}\n"
            error_msg += f"💬 *Error Message:* {str(error)}\n"
            
            if update_data:
                error_msg += f"📝 *Update Data:* {update_data[:500]}...\n" if len(update_data) > 500 else f"📝 *Update Data:* {update_data}\n"
            
            # Add traceback (truncated if too long)
            tb = traceback.format_exc()
            if len(tb) > 1000:
                tb = tb[:1000] + "...\n[Traceback truncated]"
            error_msg += f"🔍 *Traceback:*\n```\n{tb}\n```"
            
            # Send to admin
            await self.bot.send_message(
                chat_id=self.admin_id,
                text=error_msg,
                parse_mode="Markdown"
            )
            
        except TelegramError as e:
            # If sending to admin fails, log it
            logger.error(f"Failed to send error notification to admin: {str(e)}")
        except Exception as e:
            # If there's any other error in error handling, log it
            logger.error(f"Error in admin error notification system: {str(e)}")
    
    async def send_unauthorized_access_alert(self, user_id: int, username: str = None, first_name: str = None, command: str = None) -> None:
        """Send unauthorized access alert to admin."""
        try:
            alert_msg = f"🔒 *Unauthorized Access Alert*\n\n"
            alert_msg += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            alert_msg += f"👤 *User ID:* {user_id}\n"
            alert_msg += f"👤 *Username:* @{username or 'unknown'}\n"
            alert_msg += f"👤 *First Name:* {first_name or 'Unknown'}\n"
            alert_msg += f"🔧 *Command:* {command or 'unknown'}\n\n"
            alert_msg += f"💡 *Action:* User was shown access denied message\n"
            alert_msg += f"📝 *Note:* Use /approve {user_id} to grant access"
            
            await self.bot.send_message(
                chat_id=self.admin_id,
                text=alert_msg,
                parse_mode="Markdown"
            )
            
        except TelegramError as e:
            logger.error(f"Failed to send unauthorized access alert to admin: {str(e)}")
        except Exception as e:
            logger.error(f"Error in unauthorized access alert system: {str(e)}")
    
    async def send_user_approved_notification(self, approved_user_id: int, username: str = None, first_name: str = None) -> None:
        """Send notification when a user is approved."""
        try:
            msg = f"✅ *User Approved*\n\n"
            msg += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            msg += f"👤 *User ID:* {approved_user_id}\n"
            msg += f"👤 *Username:* @{username or 'unknown'}\n"
            msg += f"👤 *First Name:* {first_name or 'Unknown'}\n"
            msg += f"🎉 *Status:* User now has access to the bot"
            
            await self.bot.send_message(
                chat_id=self.admin_id,
                text=msg,
                parse_mode="Markdown"
            )
            
        except TelegramError as e:
            logger.error(f"Failed to send user approved notification to admin: {str(e)}")
        except Exception as e:
            logger.error(f"Error in user approved notification system: {str(e)}")
    
    async def send_user_removed_notification(self, removed_user_id: int, username: str = None, first_name: str = None) -> None:
        """Send notification when a user is removed."""
        try:
            msg = f"❌ *User Access Removed*\n\n"
            msg += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            msg += f"👤 *User ID:* {removed_user_id}\n"
            msg += f"👤 *Username:* @{username or 'unknown'}\n"
            msg += f"👤 *First Name:* {first_name or 'Unknown'}\n"
            msg += f"🚫 *Status:* User access revoked"
            
            await self.bot.send_message(
                chat_id=self.admin_id,
                text=msg,
                parse_mode="Markdown"
            )
            
        except TelegramError as e:
            logger.error(f"Failed to send user removed notification to admin: {str(e)}")
        except Exception as e:
            logger.error(f"Error in user removed notification system: {str(e)}")

# Global admin notifier instance
admin_notifier: Optional[AdminNotifier] = None

def initialize_admin_notifier(bot: Bot) -> None:
    """Initialize the global admin notifier."""
    global admin_notifier
    admin_notifier = AdminNotifier(bot)

def get_admin_notifier() -> Optional[AdminNotifier]:
    """Get the global admin notifier instance."""
    return admin_notifier

async def notify_admin_error(error: Exception, context: str = None, user_id: int = None, update_data: str = None) -> None:
    """Convenience function to notify admin of errors."""
    notifier = get_admin_notifier()
    if notifier:
        await notifier.send_error_notification(error, context, user_id, update_data)

async def notify_admin_unauthorized_access(user_id: int, username: str = None, first_name: str = None, command: str = None) -> None:
    """Convenience function to notify admin of unauthorized access."""
    notifier = get_admin_notifier()
    if notifier:
        await notifier.send_unauthorized_access_alert(user_id, username, first_name, command)
