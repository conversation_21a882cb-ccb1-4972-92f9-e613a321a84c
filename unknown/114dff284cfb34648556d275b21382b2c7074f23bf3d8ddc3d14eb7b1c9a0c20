import os
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import prompt configuration
try:
    import prompt_config
except ImportError:
    prompt_config = None

# Get API key from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"

def generate_content(prompt):
    """
    Generate content using the Gemini 2.5 Flash API.

    Args:
        prompt (str): The prompt to send to the Gemini API

    Returns:
        str: The generated content or error message
    """
    if not GEMINI_API_KEY:
        return "Error: Gemini API key not found. Please set the GEMINI_API_KEY environment variable."

    url = f"{GEMINI_API_URL}?key={GEMINI_API_KEY}"

    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "contents": [{
            "parts": [{"text": prompt}]
        }]
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # Raise an exception for HTTP errors

        result = response.json()

        # Extract the generated text from the response
        if "candidates" in result and len(result["candidates"]) > 0:
            if "content" in result["candidates"][0] and "parts" in result["candidates"][0]["content"]:
                parts = result["candidates"][0]["content"]["parts"]
                if parts and "text" in parts[0]:
                    return parts[0]["text"]

        return "Error: Unable to extract content from API response."

    except requests.exceptions.RequestException as e:
        return f"Error: API request failed - {str(e)}"
    except json.JSONDecodeError:
        return "Error: Invalid JSON response from API"
    except Exception as e:
        return f"Error: {str(e)}"

def generate_daily_news_summary(country="India", custom_prompt=None):
    """
    Generate a daily news summary for a specific country.

    Args:
        country (str): The country to generate news for (default: India)
        custom_prompt (str): Custom prompt for this project

    Returns:
        str: The generated news summary
    """
    try:
        # Try to use the news API first
        import news_api
        return news_api.generate_news_summary(country, custom_prompt)
    except Exception as e:
        # If the news API fails, fall back to Gemini
        print(f"News API failed: {e}. Falling back to Gemini.")

        # Get configurable prompt for daily news
        if prompt_config:
            prompt = prompt_config.get_daily_news_prompt(country, custom_prompt)
        else:
            # Fallback to hardcoded prompt if prompt_config is not available
            current_date = datetime.now().strftime("%A, %B %d, %Y")
            prompt = f"""Generate a daily news summary for {country} for TODAY, {current_date}.

IMPORTANT: You MUST include ONLY THE MOST RECENT NEWS from the past 24 hours. Do NOT include old news from previous days or weeks.
Use your knowledge cutoff to determine what is current and what is not. If you're unsure if something is current, do not include it.

Format the summary EXACTLY as follows:
- Start with "*DAILY NEWS SUMMARY*" (bold title)
- Each news item should be in italics using underscores: _News item text here._
- Add one blank line between each news item
- Include relevant emojis for each news item
- Focus on the most important and impactful news
- Include a mix of categories: politics, economy, technology, sports, entertainment, etc.

IMPORTANT: The entire summary MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

CRITICAL: All news items MUST be from the LAST 24-48 HOURS ONLY. Do NOT include old news."""

        content = generate_content(prompt)

        # Ensure the content is within the 1024 character limit
        if len(content) > 1024:
            # Truncate if too long
            content = content[:1021] + "..."

        return content

def generate_crypto_prices():
    """
    Generate a daily cryptocurrency price update.

    Returns:
        str: The generated crypto price update
    """
    # Get configurable prompt for crypto prices
    if prompt_config:
        prompt = prompt_config.get_crypto_prices_prompt()
    else:
        # Fallback to hardcoded prompt if prompt_config is not available
        current_date = datetime.now().strftime("%A, %B %d, %Y")
        prompt = f"""Generate a daily cryptocurrency price update for {current_date}.

IMPORTANT: You MUST provide the MOST CURRENT cryptocurrency information you have access to.
If you're unsure about current prices, focus on the trends and general market conditions instead.

Format the update EXACTLY as follows:
- Start with "💰 *CRYPTO MARKET UPDATE*" (bold title)
- Include major cryptocurrencies: Bitcoin (BTC), Ethereum (ETH), Solana (SOL), etc.
- Show prices in USD format: $XX,XXX.XX
- Include 24h percentage changes with + or - signs
- Add relevant emojis for each coin
- Include market sentiment and trends
- Add one trending/notable coin
- Include top gainer and top loser

IMPORTANT: The entire update MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

CRITICAL: Focus on providing the MOST CURRENT information available to you."""

    content = generate_content(prompt)

    # Ensure the content is within the 1024 character limit
    if len(content) > 1024:
        # Truncate if too long
        content = content[:1021] + "..."

    return content

def generate_health_fitness_content(custom_prompt=None):
    """
    Generate health, nutrition, and fitness content.

    Args:
        custom_prompt (str): Custom prompt for this project

    Returns:
        str: The generated health and fitness content
    """
    # Get configurable prompt for health & fitness
    if prompt_config:
        prompt = prompt_config.get_health_fitness_prompt(custom_prompt)
    else:
        # Fallback to hardcoded prompt if prompt_config is not available
        current_date = datetime.now().strftime("%A, %B %d, %Y")
        prompt = f"""Generate valuable health, nutrition, and fitness content for {current_date}.

IMPORTANT: Create a well-structured, informative post that provides genuinely useful health and fitness advice.
Focus on creating content that is readable, valuable, and presents new information each time.

Format requirements:
- Start with "💪 *HEALTH & FITNESS*" (bold title)
- Use clear sections with emojis
- Include practical tips that people can actually implement
- Make it engaging and motivational
- Vary the topics each time (nutrition, exercise, mental health, wellness tips, etc.)
- Use italics for emphasis: _important points_
- Include relevant emojis throughout

IMPORTANT: The entire post MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit.

Focus on providing ACTIONABLE advice that adds real value to readers' health and fitness journey."""

    content = generate_content(prompt)

    # Ensure the content is within the 1024 character limit
    if len(content) > 1024:
        # Truncate if too long
        content = content[:1021] + "..."

    return content

def generate_custom_content(custom_prompt=None):
    """
    Generate custom content using user-defined prompts.

    Args:
        custom_prompt: Custom prompt for content generation. If None, uses default prompt.

    Returns:
        str: Generated custom content
    """
    # Use the prompt configuration system to get the appropriate prompt
    from prompt_config import get_prompt

    try:
        # Get the prompt using the prompt configuration system
        prompt = get_prompt("custom_content")

        # If a custom prompt is provided, use it instead
        if custom_prompt and custom_prompt.strip():
            prompt = custom_prompt

        content = generate_content(prompt)

        # Ensure the content is within the 1024 character limit
        if len(content) > 1024:
            # Truncate if too long
            content = content[:1021] + "..."

        return content

    except Exception as e:
        logger.error(f"Error generating custom content: {e}")
        return "🎨 *CUSTOM CONTENT*\n\n_Unable to generate custom content at the moment. Please try again later._"
